import 'package:get/get.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/modules/role/repository/role_repository.dart';
import 'package:rolio/modules/role/repository/recommend_repository.dart';
import 'package:rolio/common/utils/image_preloader.dart';
import 'package:rolio/common/utils/retry_util.dart';

/// 页面状态枚举
enum RolePageState {
  loading,      // 加载中
  loaded,       // 加载完成
  error,        // 错误状态
  favoriteLoading, // 收藏操作中
}

/// 图片预加载状态类
class ImagePreloadState {
  final bool isCoverPreloaded;
  final bool isAvatarPreloaded;
  final Set<String> preloadingUrls;
  final Map<String, int> retryCount;

  const ImagePreloadState({
    this.isCoverPreloaded = false,
    this.isAvatarPreloaded = false,
    this.preloadingUrls = const {},
    this.retryCount = const {},
  });

  ImagePreloadState copyWith({
    bool? isCoverPreloaded,
    bool? isAvatarPreloaded,
    Set<String>? preloadingUrls,
    Map<String, int>? retryCount,
  }) {
    return ImagePreloadState(
      isCoverPreloaded: isCoverPreloaded ?? this.isCoverPreloaded,
      isAvatarPreloaded: isAvatarPreloaded ?? this.isAvatarPreloaded,
      preloadingUrls: preloadingUrls ?? this.preloadingUrls,
      retryCount: retryCount ?? this.retryCount,
    );
  }
}

/// 全局收藏状态管理器
///
/// 管理所有角色的收藏状态，提供统一的收藏状态缓存和查询接口
/// 作为唯一的收藏状态数据源，确保状态一致性
class GlobalFavoriteStateManager {
  // 收藏状态缓存 Map<roleId, isFavorited>
  final RxMap<int, bool> favoriteStatusCache = <int, bool>{}.obs;

  // 最后更新时间缓存 Map<roleId, timestamp>
  final Map<int, int> _lastUpdateTime = {};

  // 缓存有效期（毫秒）
  static const int cacheValidityDuration = 5 * 60 * 1000; // 5分钟

  // 状态变更通知流
  final RxBool _stateChanged = false.obs;
  
  /// 设置角色收藏状态（统一入口）
  void setFavoriteStatus(int roleId, bool isFavorited) {
    final oldStatus = favoriteStatusCache[roleId];
    favoriteStatusCache[roleId] = isFavorited;
    _lastUpdateTime[roleId] = DateTime.now().millisecondsSinceEpoch;

    // 如果状态发生变化，触发通知
    if (oldStatus != isFavorited) {
      _triggerStateChange();
    }

    LogUtil.debug('GlobalFavoriteStateManager: 更新收藏状态缓存 roleId=$roleId, isFavorited=$isFavorited');
  }

  /// 触发状态变更通知
  void _triggerStateChange() {
    _stateChanged.value = !_stateChanged.value;
  }
  
  /// 获取角色收藏状态
  bool? getFavoriteStatus(int roleId) {
    return favoriteStatusCache[roleId];
  }
  
  /// 检查缓存是否有效
  bool isCacheValid(int roleId) {
    final lastUpdate = _lastUpdateTime[roleId];
    if (lastUpdate == null) return false;
    
    final now = DateTime.now().millisecondsSinceEpoch;
    return (now - lastUpdate) < CACHE_VALIDITY_DURATION;
  }
  
  /// 移除角色收藏状态缓存
  void removeFavoriteStatus(int roleId) {
    favoriteStatusCache.remove(roleId);
    _lastUpdateTime.remove(roleId);
  }
  
  /// 清除所有缓存
  void clearCache() {
    favoriteStatusCache.clear();
    _lastUpdateTime.clear();
    _triggerStateChange(); // 清除缓存也是状态变更
  }

  /// 获取状态变更通知流（用于UI监听）
  RxBool get stateChangeNotifier => _stateChanged;
  
  /// 批量更新收藏状态
  void batchUpdateFavoriteStatus(Map<int, bool> statusMap) {
    final now = DateTime.now().millisecondsSinceEpoch;
    bool hasChanges = false;

    statusMap.forEach((roleId, isFavorited) {
      final oldStatus = favoriteStatusCache[roleId];
      favoriteStatusCache[roleId] = isFavorited;
      _lastUpdateTime[roleId] = now;

      if (oldStatus != isFavorited) {
        hasChanges = true;
      }
    });

    // 如果有状态变化，触发通知
    if (hasChanges) {
      _triggerStateChange();
    }

    LogUtil.debug('GlobalFavoriteStateManager: 批量更新收藏状态缓存，共${statusMap.length}个角色');
  }
  
  /// 获取所有收藏的角色ID列表
  List<int> getFavoritedRoleIds() {
    return favoriteStatusCache.entries
        .where((entry) => entry.value == true)
        .map((entry) => entry.key)
        .toList();
  }
  
  /// 获取缓存大小
  int get cacheSize => favoriteStatusCache.length;
}

/// 角色详情状态管理器
///
/// 聚合管理角色详情页的所有状态，提供统一的状态更新和访问接口
class RoleStateManager {
  // 核心状态变量
  final Rx<AiRole?> currentRole = Rx<AiRole?>(null);
  final Rx<RolePageState> pageState = RolePageState.loading.obs;
  final RxString errorMessage = ''.obs;
  final Rx<ImagePreloadState> imagePreloadState = const ImagePreloadState().obs;

  // 重试相关
  int _retryCount = 0;
  dynamic _lastError;

  // 常量
  static const int maxPreloadRetry = 2;
  static const int maxRetry = 3;

  // 图片预加载器
  ImagePreloader get _imagePreloader => Get.find<ImagePreloader>();
  
  // Computed属性
  bool get isLoading => pageState.value == RolePageState.loading;
  bool get isFavoriteLoading => pageState.value == RolePageState.favoriteLoading;
  bool get hasError => pageState.value == RolePageState.error;
  bool get isCoverPreloaded => imagePreloadState.value.isCoverPreloaded;
  int get retryCount => _retryCount;
  
  /// 设置角色数据并触发图片预加载
  void setRole(AiRole? role) {
    currentRole.value = role;
    if (role != null) {
      _preloadRoleImages(role);
    }
  }
  
  /// 设置页面状态
  void setPageState(RolePageState state) {
    pageState.value = state;
  }
  
  /// 设置错误信息
  void setError(String message, {dynamic originalError}) {
    pageState.value = RolePageState.error;
    errorMessage.value = message;
    if (originalError != null) {
      setLastError(originalError);
    }
  }
  
  /// 设置加载状态
  void setLoading() {
    pageState.value = RolePageState.loading;
    errorMessage.value = '';
  }
  
  /// 设置已加载状态
  void setLoaded() {
    pageState.value = RolePageState.loaded;
    errorMessage.value = '';
    _retryCount = 0; // 成功后重置重试次数
  }
  
  /// 设置收藏加载状态
  void setFavoriteLoading(bool loading) {
    if (loading) {
      pageState.value = RolePageState.favoriteLoading;
    } else {
      pageState.value = RolePageState.loaded;
    }
  }
  
  /// 增加重试计数
  void incrementRetryCount() {
    _retryCount++;
  }

  /// 重置重试计数
  void resetRetryCount() {
    _retryCount = 0;
    _lastError = null;
  }

  /// 设置最后的错误信息
  void setLastError(dynamic error) {
    _lastError = error;
  }

  /// 检查是否可以重试
  bool canRetry() {
    // 基本重试次数检查
    if (_retryCount >= maxRetry) {
      return false;
    }

    // 如果有错误信息，检查错误是否可重试
    if (_lastError != null && !RetryUtil.shouldRetry(_lastError)) {
      return false;
    }

    return true;
  }

  /// 异步检查是否可以重试（包含网络状态检查）
  Future<bool> canRetryAsync() async {
    // 基本检查
    if (!canRetry()) {
      return false;
    }

    // 网络状态检查
    final networkAvailable = await RetryUtil.isNetworkConnected();
    if (!networkAvailable) {
      LogUtil.warn('RoleStateManager: 网络不可用，无法重试');
      return false;
    }

    return true;
  }

  /// 计算重试延迟时间
  int calculateRetryDelay() {
    return RetryUtil.calculateRetryDelay(
      _retryCount,
      500, // 基础延迟500ms
      10000, // 最大延迟10秒
      enableJitter: true,
    );
  }
  
  /// 预加载角色图片
  void _preloadRoleImages(AiRole role) {
    final currentState = imagePreloadState.value;
    final newPreloadingUrls = Set<String>.from(currentState.preloadingUrls);
    final newRetryCount = Map<String, int>.from(currentState.retryCount);

    // 重置图片预加载状态
    imagePreloadState.value = const ImagePreloadState();

    // 预加载封面图
    if (role.coverUrl.isNotEmpty) {
      final isCoverPreloaded = _imagePreloader.isImagePreloaded(role.coverUrl);

      if (!isCoverPreloaded && !newPreloadingUrls.contains(role.coverUrl)) {
        LogUtil.debug('开始预加载封面图: ${role.coverUrl}');
        newPreloadingUrls.add(role.coverUrl);

        _imagePreloader.preloadImage(
          role.coverUrl,
          priority: ImagePreloadPriority.high,
          onComplete: (success) => _handleImagePreloadComplete(
            role.coverUrl,
            success,
            isHighPriority: true,
            isCover: true,
          ),
        );
      }

      // 更新封面预加载状态
      imagePreloadState.value = currentState.copyWith(
        isCoverPreloaded: isCoverPreloaded,
        preloadingUrls: newPreloadingUrls,
        retryCount: newRetryCount,
      );
    }

    // 预加载头像图片
    if (role.avatarUrl.isNotEmpty) {
      final isAvatarPreloaded = _imagePreloader.isImagePreloaded(role.avatarUrl);

      if (!isAvatarPreloaded && !newPreloadingUrls.contains(role.avatarUrl)) {
        LogUtil.debug('开始预加载头像: ${role.avatarUrl}');
        newPreloadingUrls.add(role.avatarUrl);

        _imagePreloader.preloadImage(
          role.avatarUrl,
          priority: ImagePreloadPriority.medium,
          onComplete: (success) => _handleImagePreloadComplete(
            role.avatarUrl,
            success,
            isHighPriority: false,
            isCover: false,
          ),
        );
      }

      // 更新头像预加载状态
      imagePreloadState.value = imagePreloadState.value.copyWith(
        isAvatarPreloaded: isAvatarPreloaded,
        preloadingUrls: newPreloadingUrls,
        retryCount: newRetryCount,
      );
    }
  }
  
  /// 处理图片预加载完成
  void _handleImagePreloadComplete(String imageUrl, bool success, {
    required bool isHighPriority,
    required bool isCover,
  }) {
    final currentState = imagePreloadState.value;
    final newPreloadingUrls = Set<String>.from(currentState.preloadingUrls);
    final newRetryCount = Map<String, int>.from(currentState.retryCount);

    // 从预加载集合中移除
    newPreloadingUrls.remove(imageUrl);

    if (success) {
      LogUtil.debug('图片预加载成功: $imageUrl');
      // 重置重试计数
      newRetryCount.remove(imageUrl);

      // 更新预加载状态
      imagePreloadState.value = currentState.copyWith(
        isCoverPreloaded: isCover ? true : currentState.isCoverPreloaded,
        isAvatarPreloaded: !isCover ? true : currentState.isAvatarPreloaded,
        preloadingUrls: newPreloadingUrls,
        retryCount: newRetryCount,
      );
    } else {
      // 预加载失败，尝试重试
      final retryCount = newRetryCount[imageUrl] ?? 0;
      if (retryCount < maxPreloadRetry) {
        newRetryCount[imageUrl] = retryCount + 1;
        LogUtil.debug('图片预加载失败，准备第${retryCount + 1}次重试: $imageUrl');

        // 更新状态
        imagePreloadState.value = currentState.copyWith(
          preloadingUrls: newPreloadingUrls,
          retryCount: newRetryCount,
        );

        // 延迟重试
        Future.delayed(Duration(milliseconds: 500 * (retryCount + 1)), () {
          if (!Get.isRegistered<RoleService>()) return;

          final updatedState = imagePreloadState.value;
          final updatedPreloadingUrls = Set<String>.from(updatedState.preloadingUrls);
          updatedPreloadingUrls.add(imageUrl);

          imagePreloadState.value = updatedState.copyWith(
            preloadingUrls: updatedPreloadingUrls,
          );

          _imagePreloader.preloadImage(
            imageUrl,
            priority: isHighPriority ? ImagePreloadPriority.high : ImagePreloadPriority.medium,
            onComplete: (success) => _handleImagePreloadComplete(
              imageUrl,
              success,
              isHighPriority: isHighPriority,
              isCover: isCover,
            ),
          );
        });
      } else {
        LogUtil.warn('图片预加载失败，已达到最大重试次数: $imageUrl');
        newRetryCount.remove(imageUrl);
        imagePreloadState.value = currentState.copyWith(
          preloadingUrls: newPreloadingUrls,
          retryCount: newRetryCount,
        );
      }
    }
  }
  
  /// 重置所有状态
  void reset() {
    currentRole.value = null;
    pageState.value = RolePageState.loading;
    errorMessage.value = '';
    imagePreloadState.value = const ImagePreloadState();
    _retryCount = 0;
  }
}

/// 角色服务类
///
/// 负责处理AI角色详情的业务逻辑
class RoleService extends GetxService {
  // 角色仓库
  final RoleRepository _repository;
  
  // 推荐仓库 - 用于获取基本角色信息
  final RecommendRepository _recommendRepository;
  
  // 状态管理器 - 统一管理角色相关状态
  final RoleStateManager _stateManager = RoleStateManager();
  
  // 全局收藏状态管理器
  final GlobalFavoriteStateManager _globalFavoriteManager = GlobalFavoriteStateManager();
  
  // 对外暴露的状态访问器
  RoleStateManager get stateManager => _stateManager;
  GlobalFavoriteStateManager get globalFavoriteManager => _globalFavoriteManager;
  
  // 兼容性属性 - 保持对外接口不变
  Rx<bool> get isLoading => Rx<bool>(_stateManager.isLoading);
  Rx<bool> get isFavoriteLoading => Rx<bool>(_stateManager.isFavoriteLoading);
  Rx<AiRole?> get currentRole => _stateManager.currentRole;
  RxString get errorMessage => _stateManager.errorMessage;
  
  // 构造函数
  RoleService({
    RoleRepository? repository, 
    RecommendRepository? recommendRepository
  }) : _repository = repository ?? Get.find<RoleRepository>(),
       _recommendRepository = recommendRepository ?? Get.find<RecommendRepository>() {
    LogUtil.debug('RoleService 构造函数执行');
  }
  
  @override
  void onInit() {
    super.onInit();
    LogUtil.info('RoleService初始化');
  }
  
  /// 获取角色详情
  /// 
  /// 通过API获取指定角色的详细信息，包括收藏状态
  /// [roleId] 角色ID
  /// [forceRefresh] 是否强制刷新，不使用缓存
  /// 返回角色详情，如果不存在则返回null
  Future<AiRole?> getRoleDetail(int roleId, {bool forceRefresh = false}) async {
    try {
      _stateManager.setLoading();
      
      LogUtil.debug('获取角色详情，ID: $roleId，强制刷新: $forceRefresh');
      
      // 调用角色仓库获取角色详情
      final role = await _repository.getRoleDetail(roleId, forceRefresh: forceRefresh);
      
      // 更新当前角色
      if (role != null) {
        _stateManager.setRole(role);
        _stateManager.setLoaded();
        // 更新全局收藏状态缓存
        _globalFavoriteManager.setFavoriteStatus(role.id, role.isFavorited);
        LogUtil.debug('成功获取角色详情: ${role.name}');
        return role;
      }
      
      // 如果没有找到角色，尝试从推荐仓库获取基本信息
      LogUtil.warn('未找到角色详情，尝试从推荐仓库获取基本信息，ID: $roleId');
      final basicRole = await _recommendRepository.getById(roleId);
      
      if (basicRole != null) {
        _stateManager.setRole(basicRole);
        _stateManager.setLoaded();
        // 更新全局收藏状态缓存
        _globalFavoriteManager.setFavoriteStatus(basicRole.id, basicRole.isFavorited);
        LogUtil.debug('成功从推荐仓库获取基本角色信息: ${basicRole.name}');
        return basicRole;
      }
      
      // 两个仓库都没有找到角色信息
      LogUtil.warn('未找到角色详情，ID: $roleId');
      _stateManager.setError('Role not found');
      return null;
      
    } catch (e) {
      LogUtil.error('获取角色详情失败: $e');
      _stateManager.setError('获取角色信息失败，请稍后再试', originalError: e);

      // 尝试从推荐仓库获取基本信息
      try {
        LogUtil.debug('尝试从推荐仓库获取基本信息，ID: $roleId');
        final basicRole = await _recommendRepository.getById(roleId);
        if (basicRole != null) {
          LogUtil.debug('成功从推荐仓库获取基本信息: ${basicRole.name}');
          _stateManager.setRole(basicRole);
          _stateManager.setLoaded();
          // 更新全局收藏状态缓存
          _globalFavoriteManager.setFavoriteStatus(basicRole.id, basicRole.isFavorited);
          return basicRole;
        }
      } catch (fallbackError) {
        LogUtil.error('从推荐仓库获取基本信息也失败: $fallbackError');
      }

      ErrorHandler.handleException(
        AppException('Failed to load role detail', originalError: e)
      );
      return null;
    }
  }
  
  /// 收藏角色
  /// 
  /// 将指定角色添加到收藏夹
  /// [roleId] 角色ID
  /// 返回是否收藏成功
  Future<bool> favoriteRole(int roleId) async {
    try {
      _stateManager.setFavoriteLoading(true);
      
      LogUtil.debug('收藏角色, ID: $roleId');
      
      // 调用仓库层收藏角色
      final result = await _repository.favoriteRole(roleId);
      
      if (result != null && result['success'] == true) {
        LogUtil.debug('成功收藏角色');
        
        // 更新当前角色的收藏状态
        if (_stateManager.currentRole.value != null && _stateManager.currentRole.value!.id == roleId) {
          // 解析收藏时间
          DateTime? favoritedAt;
          if (result['favorited_at'] != null) {
            try {
              favoritedAt = DateTime.parse(result['favorited_at'].toString());
            } catch (e) {
              LogUtil.warn('解析收藏时间失败: ${result['favorited_at']}');
              favoritedAt = DateTime.now();
            }
          } else {
            favoritedAt = DateTime.now();
          }
          
          // 更新角色对象
          final updatedRole = _stateManager.currentRole.value!.copyWith(
            isFavorited: true,
            favoritedAt: favoritedAt,
          );
          _stateManager.setRole(updatedRole);
          
          // 更新全局收藏状态缓存
          _globalFavoriteManager.setFavoriteStatus(roleId, true);
        }
        
        return true;
      } else {
        LogUtil.warn('收藏角色失败');
        return false;
      }
    } catch (e) {
      LogUtil.error('收藏角色异常: $e');
      _stateManager.setError('收藏角色失败，请稍后再试');
      ErrorHandler.handleException(e);
      return false;
    } finally {
      _stateManager.setFavoriteLoading(false);
    }
  }
  
  /// 取消收藏角色
  /// 
  /// 将指定角色从收藏夹中移除
  /// [roleId] 角色ID
  /// 返回是否取消成功
  Future<bool> unfavoriteRole(int roleId) async {
    try {
      _stateManager.setFavoriteLoading(true);
      
      LogUtil.debug('取消收藏角色, ID: $roleId');
      
      // 调用仓库层取消收藏角色
      final success = await _repository.unfavoriteRole(roleId);
      
      if (success) {
        LogUtil.debug('成功取消收藏角色');
        
        // 更新当前角色的收藏状态
        if (_stateManager.currentRole.value != null && _stateManager.currentRole.value!.id == roleId) {
          final updatedRole = _stateManager.currentRole.value!.copyWith(
            isFavorited: false,
            favoritedAt: null,
          );
          _stateManager.setRole(updatedRole);
          
          // 更新全局收藏状态缓存
          _globalFavoriteManager.setFavoriteStatus(roleId, false);
        }
        
        return true;
      } else {
        LogUtil.warn('取消收藏角色失败');
        return false;
      }
    } catch (e) {
      LogUtil.error('取消收藏角色异常: $e');
      _stateManager.setError('取消收藏角色失败，请稍后再试');
      ErrorHandler.handleException(e);
      return false;
    } finally {
      _stateManager.setFavoriteLoading(false);
    }
  }
  
  /// 切换收藏状态（统一错误处理版本）
  ///
  /// 如果角色已收藏则取消收藏，否则收藏角色
  /// [roleId] 角色ID
  /// 返回操作后的收藏状态(true=已收藏, false=未收藏)
  Future<bool?> toggleFavorite(int roleId) async {
    try {
      _stateManager.setFavoriteLoading(true);

      LogUtil.debug('RoleService: 切换收藏状态, ID: $roleId');

      // 获取当前角色信息
      AiRole? roleInfo = _stateManager.currentRole.value;
      if (roleInfo == null || roleInfo.id != roleId) {
        roleInfo = await getRoleDetail(roleId);
        if (roleInfo == null) {
          LogUtil.error('RoleService: 无法获取角色信息，切换收藏状态失败');
          return null;
        }
      }

      // 获取当前收藏状态
      final isFavorited = roleInfo.isFavorited;

      // 执行收藏操作
      bool success;
      if (isFavorited) {
        success = await unfavoriteRole(roleId);
      } else {
        success = await favoriteRole(roleId);
      }

      if (success) {
        // 操作成功，返回新状态
        final newState = !isFavorited;
        LogUtil.debug('RoleService: 收藏状态切换成功, ID: $roleId, 新状态: $newState');
        return newState;
      } else {
        // 操作失败，尝试获取最新状态
        LogUtil.warn('RoleService: 收藏操作失败，尝试获取最新状态, ID: $roleId');
        return await getFavoriteStatus(roleId, forceRefresh: true);
      }
    } catch (e) {
      LogUtil.error('RoleService: 切换收藏状态异常: $e');
      // 发生异常时，尝试获取最新状态
      try {
        return await getFavoriteStatus(roleId, forceRefresh: true);
      } catch (syncError) {
        LogUtil.error('RoleService: 获取最新收藏状态也失败: $syncError');
        return null;
      }
    } finally {
      _stateManager.setFavoriteLoading(false);
    }
  }

  /// 获取角色收藏状态（简化版本）
  /// 
  /// 优先从缓存获取，缓存无效时从服务器同步
  /// [roleId] 角色ID
  /// [forceRefresh] 是否强制从服务器刷新
  /// 返回收藏状态，失败时返回null
  Future<bool?> getFavoriteStatus(int roleId, {bool forceRefresh = false}) async {
    try {
      // 如果不强制刷新且缓存有效，直接返回缓存
      if (!forceRefresh && _globalFavoriteManager.isCacheValid(roleId)) {
        final cachedStatus = _globalFavoriteManager.getFavoriteStatus(roleId);
        if (cachedStatus != null) {
          LogUtil.debug('RoleService: 从缓存获取收藏状态 roleId=$roleId, status=$cachedStatus');
          return cachedStatus;
        }
      }
      
      // 从服务器获取最新状态
      LogUtil.debug('RoleService: 从服务器同步收藏状态 roleId=$roleId');
      final role = await getRoleDetail(roleId, forceRefresh: true);
      
      if (role != null) {
        // 状态已在getRoleDetail中更新到缓存
        return role.isFavorited;
      }
      
      return null;
    } catch (e) {
      LogUtil.error('RoleService: 获取收藏状态失败: $e');
      return null;
    }
  }
  
  /// 获取角色的收藏状态（同步版本）
  /// 
  /// 优先从全局缓存获取，缓存不存在时返回false
  /// [roleId] 角色ID
  /// 返回角色的收藏状态
  bool getRoleFavoriteStatus(int roleId) {
    // 优先从全局缓存获取
    final cachedStatus = _globalFavoriteManager.getFavoriteStatus(roleId);
    if (cachedStatus != null) {
      return cachedStatus;
    }
    
    // 如果是当前角色，从StateManager获取
    final role = _stateManager.currentRole.value;
    if (role != null && role.id == roleId) {
      return role.isFavorited;
    }
    
    return false;
  }
  
  /// 获取收藏的角色列表
  /// [page] 页码，从1开始
  /// [size] 每页大小
  /// 返回分页的收藏角色列表数据
  Future<Map<String, dynamic>> getFavoritedRoles({int page = 1, int size = 10}) async {
    try {
      LogUtil.debug('获取收藏角色列表，page: $page, size: $size');
      final result = await _repository.getFavoritedRoles(page: page, size: size);
      
      // 批量更新收藏状态缓存
      final roles = result['items'] as List<AiRole>;
      if (roles.isNotEmpty) {
        final statusMap = <int, bool>{};
        for (final role in roles) {
          statusMap[role.id] = role.isFavorited;
        }
        _globalFavoriteManager.batchUpdateFavoriteStatus(statusMap);
      }
      
      return result;
    } catch (e) {
      LogUtil.error('获取收藏角色列表失败: $e');

      // 返回空数据结构
      return {
        'items': <AiRole>[],
        'total': 0,
        'page': page,
        'size': size,
        'pages': 0
      };
    }
  }
  
  /// 清除当前角色信息
  void clearCurrentRole() {
    _stateManager.reset();
  }
  
  /// 添加重试获取角色详情的方法
  Future<void> retryGetRoleDetail(int roleId) async {
    // 异步检查是否可以重试（包含网络状态检查）
    final canRetry = await _stateManager.canRetryAsync();
    if (!canRetry) {
      LogUtil.warn('无法重试获取角色详情: 已达到最大重试次数或网络不可用');
      return;
    }

    // 计算延迟时间
    final delayMs = _stateManager.calculateRetryDelay();
    _stateManager.incrementRetryCount();

    LogUtil.info('重试获取角色详情，第${_stateManager.retryCount}次: ID=$roleId，延迟${delayMs}ms');

    // 延迟后重试
    if (delayMs > 0) {
      await Future.delayed(Duration(milliseconds: delayMs));
    }

    await getRoleDetail(roleId, forceRefresh: true);
  }

  @override
  void onClose() {
    try {
      // 清理GlobalFavoriteStateManager资源
      _globalFavoriteManager.clearCache();
      LogUtil.debug('RoleService: GlobalFavoriteStateManager缓存已清理');

      // 清理StateManager资源
      _stateManager.reset();
      LogUtil.debug('RoleService: StateManager已重置');

      LogUtil.debug('RoleService: 资源清理完成');
    } catch (e) {
      LogUtil.error('RoleService: 资源清理过程中发生错误: $e');
    } finally {
      super.onClose();
    }
  }
}
